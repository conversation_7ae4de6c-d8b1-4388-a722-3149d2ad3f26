#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库日志分析工具
分析数据库操作耗时分布，识别异常慢的请求
"""

import re
import os
import sys
import json
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Optional
import argparse
from datetime import datetime


class LogAnalyzer:
    def __init__(self):
        # 耗时分档配置 (毫秒)
        self.time_buckets = [
            (0, 10),      # 0-10ms
            (10, 30),     # 10-30ms  
            (30, 80),     # 30-80ms
            (80, 200),    # 80-200ms
            (200, 500),   # 200-500ms
            (500, 1000),  # 500ms-1s
            (1000, 3000), # 1-3s
            (3000, float('inf'))  # 3s+
        ]
        
        # 存储解析结果
        self.requests = []  # [(ip, duration_ms, log_line, line_number, file_path)]
        self.stats = defaultdict(int)
        
    def parse_log_line(self, line: str, line_number: int, file_path: str) -> Optional[Tuple[str, float, str]]:
        """
        解析日志行，提取IP和耗时信息
        返回: (ip, duration_ms, original_line) 或 None
        
        支持多种常见的日志格式:
        1. [timestamp] IP:port - duration: 123ms
        2. timestamp IP duration=123ms
        3. IP - [timestamp] - 123ms
        4. 包含 "took" "elapsed" "duration" 等关键词的格式
        """
        
        # 常见的耗时模式
        duration_patterns = [
            r'duration[:\s=]+(\d+(?:\.\d+)?)ms',
            r'took[:\s=]+(\d+(?:\.\d+)?)ms', 
            r'elapsed[:\s=]+(\d+(?:\.\d+)?)ms',
            r'time[:\s=]+(\d+(?:\.\d+)?)ms',
            r'(\d+(?:\.\d+)?)ms',
            r'duration[:\s=]+(\d+(?:\.\d+)?)s',  # 秒为单位
            r'took[:\s=]+(\d+(?:\.\d+)?)s',
            r'elapsed[:\s=]+(\d+(?:\.\d+)?)s',
        ]
        
        # IP地址模式
        ip_patterns = [
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})',
            r'IP[:\s=]+(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})',
            r'from[:\s=]+(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})',
        ]
        
        # 提取耗时
        duration_ms = None
        for pattern in duration_patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                duration = float(match.group(1))
                # 如果是秒为单位，转换为毫秒
                if 'ms' not in pattern:
                    duration *= 1000
                duration_ms = duration
                break
        
        if duration_ms is None:
            return None
            
        # 提取IP
        ip = "unknown"
        for pattern in ip_patterns:
            match = re.search(pattern, line)
            if match:
                ip = match.group(1)
                break
        
        return (ip, duration_ms, line.strip())
    
    def analyze_files(self, file_paths: List[str]) -> None:
        """分析多个日志文件"""
        print(f"开始分析 {len(file_paths)} 个日志文件...")
        
        total_lines = 0
        parsed_lines = 0
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                print(f"警告: 文件不存在 {file_path}")
                continue
                
            print(f"正在处理: {file_path}")
            
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    for line_number, line in enumerate(f, 1):
                        total_lines += 1
                        
                        result = self.parse_log_line(line, line_number, file_path)
                        if result:
                            ip, duration_ms, original_line = result
                            self.requests.append((ip, duration_ms, original_line, line_number, file_path))
                            parsed_lines += 1
                            
                        # 每处理10万行显示进度
                        if total_lines % 100000 == 0:
                            print(f"  已处理 {total_lines} 行，解析成功 {parsed_lines} 行")
                            
            except Exception as e:
                print(f"错误: 处理文件 {file_path} 时出错: {e}")
        
        print(f"分析完成! 总共处理 {total_lines} 行，成功解析 {parsed_lines} 行")
    
    def calculate_statistics(self) -> Dict:
        """计算统计信息"""
        if not self.requests:
            return {}
            
        durations = [req[1] for req in self.requests]
        durations.sort()
        
        total_count = len(durations)
        avg_duration = sum(durations) / total_count
        median_duration = durations[total_count // 2]
        
        # 计算分档统计
        bucket_stats = {}
        for start, end in self.time_buckets:
            count = sum(1 for d in durations if start <= d < end)
            if end == float('inf'):
                bucket_name = f"{start}ms+"
            else:
                bucket_name = f"{start}-{end}ms"
            bucket_stats[bucket_name] = count
        
        # 识别异常慢的请求 (最慢的5%，且明显偏离平均值)
        slow_threshold_index = int(total_count * 0.95)  # 最慢的5%
        slow_threshold = durations[slow_threshold_index] if slow_threshold_index < total_count else durations[-1]
        
        # 只有当最慢的5%明显超过平均值时才认为是异常
        is_significantly_slow = slow_threshold > avg_duration * 2
        
        slow_requests = []
        if is_significantly_slow:
            slow_requests = [req for req in self.requests if req[1] >= slow_threshold]
            slow_requests.sort(key=lambda x: x[1], reverse=True)  # 按耗时降序
        
        return {
            'total_requests': total_count,
            'avg_duration': avg_duration,
            'median_duration': median_duration,
            'min_duration': durations[0],
            'max_duration': durations[-1],
            'bucket_stats': bucket_stats,
            'slow_threshold': slow_threshold,
            'is_significantly_slow': is_significantly_slow,
            'slow_requests': slow_requests[:50],  # 最多显示50个最慢的请求
            'ip_stats': self._calculate_ip_stats()
        }
    
    def _calculate_ip_stats(self) -> Dict:
        """计算各IP的统计信息"""
        ip_data = defaultdict(list)
        for ip, duration, _, _, _ in self.requests:
            ip_data[ip].append(duration)
        
        ip_stats = {}
        for ip, durations in ip_data.items():
            ip_stats[ip] = {
                'count': len(durations),
                'avg_duration': sum(durations) / len(durations),
                'max_duration': max(durations),
                'min_duration': min(durations)
            }
        
        return ip_stats
    
    def print_report(self, stats: Dict) -> None:
        """打印分析报告"""
        print("\n" + "="*60)
        print("数据库日志耗时分析报告")
        print("="*60)
        
        print(f"\n📊 总体统计:")
        print(f"  总请求数: {stats['total_requests']:,}")
        print(f"  平均耗时: {stats['avg_duration']:.2f}ms")
        print(f"  中位数耗时: {stats['median_duration']:.2f}ms")
        print(f"  最快请求: {stats['min_duration']:.2f}ms")
        print(f"  最慢请求: {stats['max_duration']:.2f}ms")
        
        print(f"\n📈 耗时分布:")
        for bucket_name, count in stats['bucket_stats'].items():
            percentage = (count / stats['total_requests']) * 100
            bar = "█" * int(percentage / 2)  # 简单的条形图
            print(f"  {bucket_name:>12}: {count:>8,} ({percentage:>5.1f}%) {bar}")
        
        # 异常慢请求分析
        if stats['is_significantly_slow']:
            print(f"\n🐌 异常慢请求分析 (>{stats['slow_threshold']:.1f}ms):")
            print(f"  检测到 {len(stats['slow_requests'])} 个异常慢的请求")
            print(f"  阈值: {stats['slow_threshold']:.1f}ms (最慢5%且超过平均值2倍)")
            
            print(f"\n  最慢的请求详情:")
            for i, (ip, duration, log_line, line_num, file_path) in enumerate(stats['slow_requests'][:10]):
                print(f"    {i+1:2d}. {duration:>8.1f}ms | {ip:>15} | {os.path.basename(file_path)}:{line_num}")
                print(f"        {log_line[:100]}{'...' if len(log_line) > 100 else ''}")
        else:
            print(f"\n✅ 未检测到明显的异常慢请求")
            print(f"   最慢5%的阈值: {stats['slow_threshold']:.1f}ms")
            print(f"   与平均值差异不大，性能较为稳定")
        
        # IP统计 (显示请求最多的前10个IP)
        print(f"\n🌐 IP请求统计 (前10名):")
        ip_stats_sorted = sorted(stats['ip_stats'].items(), 
                               key=lambda x: x[1]['count'], reverse=True)
        for ip, ip_stat in ip_stats_sorted[:10]:
            print(f"  {ip:>15}: {ip_stat['count']:>6,}次 | "
                  f"平均{ip_stat['avg_duration']:>6.1f}ms | "
                  f"最慢{ip_stat['max_duration']:>6.1f}ms")
    
    def save_slow_requests(self, stats: Dict, output_file: str = "slow_requests.json") -> None:
        """保存异常慢请求的详细信息到文件"""
        if not stats['is_significantly_slow']:
            return
            
        slow_data = []
        for ip, duration, log_line, line_num, file_path in stats['slow_requests']:
            slow_data.append({
                'ip': ip,
                'duration_ms': duration,
                'log_line': log_line,
                'line_number': line_num,
                'file_path': file_path,
                'timestamp': datetime.now().isoformat()
            })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(slow_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 异常慢请求详情已保存到: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='数据库日志耗时分析工具')
    parser.add_argument('files', nargs='+', help='要分析的日志文件路径')
    parser.add_argument('--output', '-o', default='slow_requests.json', 
                       help='异常慢请求输出文件 (默认: slow_requests.json)')
    
    args = parser.parse_args()
    
    analyzer = LogAnalyzer()
    analyzer.analyze_files(args.files)
    
    stats = analyzer.calculate_statistics()
    if stats:
        analyzer.print_report(stats)
        analyzer.save_slow_requests(stats, args.output)
    else:
        print("未能解析到任何有效的请求数据，请检查日志格式")


if __name__ == "__main__":
    main()
