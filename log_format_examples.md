# 日志格式示例和配置说明

## 支持的日志格式

脚本已经内置了多种常见的日志格式支持，包括：

### 1. 标准格式
```
2025-01-15 10:30:45 ************* - duration: 125ms - SELECT * FROM users
[2025-01-15 10:30:45] IP: ************* took 125ms
************* - [15/Jan/2025:10:30:45] - 125ms - database query completed
```

### 2. 应用日志格式
```
INFO 2025-01-15 10:30:45 [*************] Database query elapsed: 125ms
DEBUG from ************* - query execution time=125ms
************* database operation duration 125ms
```

### 3. 秒为单位的格式
```
************* - query took 1.25s
[INFO] ************* duration: 0.125s
```

## 如何使用

### 基本用法
```bash
python log_analyzer.py 20250728.log 20250729.log 20250730.log
```

### 指定输出文件
```bash
python log_analyzer.py *.log --output my_slow_requests.json
```

### Windows PowerShell
```powershell
python log_analyzer.py 20250728.log 20250729.log 20250730.log
```

## 输出说明

### 1. 耗时分档
- 0-10ms: 极快响应
- 10-30ms: 快速响应  
- 30-80ms: 正常响应
- 80-200ms: 较慢响应
- 200-500ms: 慢响应
- 500ms-1s: 很慢响应
- 1-3s: 极慢响应
- 3s+: 超时级别

### 2. 异常检测逻辑
- 取最慢的5%请求
- 只有当这5%的阈值 > 平均耗时×2 时，才认为有异常
- 例如：平均50ms，最慢5%阈值200ms → 有异常
- 例如：平均100ms，最慢5%阈值105ms → 无异常

### 3. 输出文件
- `slow_requests.json`: 包含所有异常慢请求的详细信息
- 包括IP、耗时、原始日志行、行号、文件路径

## 自定义配置

如果你的日志格式不被支持，可以修改 `log_analyzer.py` 中的正则表达式：

```python
# 在 parse_log_line 方法中添加你的格式
duration_patterns = [
    r'your_custom_pattern_here',  # 添加你的模式
    # ... 其他模式
]

ip_patterns = [
    r'your_ip_pattern_here',      # 添加你的IP模式  
    # ... 其他模式
]
```

## 性能说明

- 支持大文件处理（GB级别）
- 每10万行显示一次进度
- 内存使用优化，逐行处理
- 支持UTF-8编码，忽略编码错误
