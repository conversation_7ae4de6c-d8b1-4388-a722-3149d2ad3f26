# 数据库日志耗时分析工具

这是一个专门用于分析数据库操作日志的Python工具，可以统计请求耗时分布，识别异常慢的请求。

## 功能特点

- ✅ **多格式支持**: 自动识别多种常见的日志格式
- ✅ **智能分档**: 按耗时自动分档统计 (0-10ms, 10-30ms, 30-80ms等)
- ✅ **异常检测**: 智能识别异常慢的请求 (最慢5%且明显偏离平均值)
- ✅ **IP统计**: 按IP地址统计请求分布
- ✅ **大文件支持**: 支持GB级别的大日志文件
- ✅ **详细报告**: 生成可视化的分析报告
- ✅ **结果保存**: 将异常请求详情保存为JSON文件

## 快速开始

### 1. 测试日志格式
首先测试你的日志格式是否能被正确解析：

```bash
# 测试前10行
python test_log_format.py 20250728.log

# 测试前50行  
python test_log_format.py 20250728.log 50
```

### 2. 运行分析
```bash
# 分析单个文件
python log_analyzer.py 20250728.log

# 分析多个文件
python log_analyzer.py 20250728.log 20250729.log 20250730.log

# 使用通配符 (PowerShell)
python log_analyzer.py *.log

# 指定输出文件
python log_analyzer.py *.log --output my_analysis.json
```

## 输出示例

```
============================================================
数据库日志耗时分析报告
============================================================

📊 总体统计:
  总请求数: 125,847
  平均耗时: 45.32ms
  中位数耗时: 38.50ms
  最快请求: 2.10ms
  最慢请求: 2,345.60ms

📈 耗时分布:
      0-10ms:   12,584 ( 10.0%) █████
     10-30ms:   50,339 ( 40.0%) ████████████████████
     30-80ms:   37,754 ( 30.0%) ███████████████
    80-200ms:   18,877 ( 15.0%) ███████
   200-500ms:    5,034 (  4.0%) ██
   500ms-1s:     1,007 (  0.8%) 
      1-3s:        252 (  0.2%) 
        3s+:          0 (  0.0%) 

🐌 异常慢请求分析 (>234.5ms):
  检测到 1,259 个异常慢的请求
  阈值: 234.5ms (最慢5%且超过平均值2倍)

  最慢的请求详情:
     1. 2,345.6ms | ************* | 20250728.log:45231
        2025-01-15 14:23:45 ************* - duration: 2345.6ms - SELECT * FROM large_table...
     2. 1,987.3ms | ************* | 20250728.log:67890
        2025-01-15 16:45:12 ************* - duration: 1987.3ms - UPDATE users SET...

🌐 IP请求统计 (前10名):
  *************:  15,234次 | 平均 42.3ms | 最慢 456.7ms
  *************:  12,567次 | 平均 38.9ms | 最慢 234.5ms
  *************:   9,876次 | 平均 52.1ms | 最慢1,987.3ms

💾 异常慢请求详情已保存到: slow_requests.json
```

## 文件说明

- `log_analyzer.py` - 主分析脚本
- `test_log_format.py` - 日志格式测试工具
- `log_format_examples.md` - 支持的日志格式说明
- `slow_requests.json` - 异常慢请求详情 (运行后生成)

## 异常检测逻辑

工具使用智能算法检测异常慢的请求：

1. **计算阈值**: 取最慢的5%请求的最小值作为阈值
2. **显著性检查**: 只有当阈值 > 平均耗时×2 时，才认为存在异常
3. **避免误报**: 如果所有请求耗时都很接近，不会误报异常

**示例**:
- 平均50ms，最慢5%阈值200ms → ✅ 检测到异常 (200 > 50×2)
- 平均100ms，最慢5%阈值105ms → ❌ 无异常 (105 < 100×2)

## 自定义配置

如果你的日志格式不被支持，可以修改 `log_analyzer.py` 中的正则表达式。

查看 `log_format_examples.md` 了解详细的自定义方法。

## 注意事项

- 确保日志文件包含IP地址和耗时信息
- 支持UTF-8编码，会自动忽略编码错误
- 大文件处理时会显示进度信息
- 建议先用测试工具验证日志格式
