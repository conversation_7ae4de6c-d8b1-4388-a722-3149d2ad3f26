#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志格式解析
用于验证你的日志格式是否能被正确解析
"""

from log_analyzer import LogAnalyzer
import sys


def test_log_format():
    """测试日志格式解析"""
    
    # 创建分析器实例
    analyzer = LogAnalyzer()
    
    # 测试样例日志行
    test_lines = [
        "2025-01-15 10:30:45 ************* - duration: 125ms - SELECT * FROM users",
        "[2025-01-15 10:30:45] IP: ************* took 125ms",
        "************* - [15/Jan/2025:10:30:45] - 125ms - database query completed",
        "INFO 2025-01-15 10:30:45 [*************] Database query elapsed: 125ms",
        "DEBUG from ************* - query execution time=125ms",
        "************* database operation duration 125ms",
        "************* - query took 1.25s",
        "[INFO] ************* duration: 0.125s",
    ]
    
    print("测试日志格式解析:")
    print("="*60)
    
    success_count = 0
    for i, line in enumerate(test_lines, 1):
        result = analyzer.parse_log_line(line, i, "test.log")
        if result:
            ip, duration_ms, original_line = result
            print(f"✅ 第{i}行: IP={ip}, 耗时={duration_ms}ms")
            success_count += 1
        else:
            print(f"❌ 第{i}行: 解析失败")
        print(f"   原始: {line}")
        print()
    
    print(f"解析成功率: {success_count}/{len(test_lines)} ({success_count/len(test_lines)*100:.1f}%)")
    
    return success_count > 0


def test_real_log_sample(file_path: str, sample_lines: int = 10):
    """测试真实日志文件的前几行"""
    
    try:
        analyzer = LogAnalyzer()
        
        print(f"\n测试真实日志文件: {file_path}")
        print("="*60)
        
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            success_count = 0
            total_count = 0
            
            for line_number, line in enumerate(f, 1):
                if line_number > sample_lines:
                    break
                    
                total_count += 1
                result = analyzer.parse_log_line(line, line_number, file_path)
                
                if result:
                    ip, duration_ms, original_line = result
                    print(f"✅ 第{line_number}行: IP={ip}, 耗时={duration_ms}ms")
                    success_count += 1
                else:
                    print(f"❌ 第{line_number}行: 解析失败")
                
                # 显示原始行（截断显示）
                display_line = line.strip()
                if len(display_line) > 80:
                    display_line = display_line[:77] + "..."
                print(f"   原始: {display_line}")
                print()
            
            print(f"前{sample_lines}行解析成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
            
            if success_count == 0:
                print("\n❗ 建议:")
                print("1. 检查日志格式是否包含IP地址和耗时信息")
                print("2. 查看 log_format_examples.md 了解支持的格式")
                print("3. 如需要，可以修改 log_analyzer.py 中的正则表达式")
            
            return success_count > 0
            
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
        return False
    except Exception as e:
        print(f"❌ 读取文件出错: {e}")
        return False


def main():
    print("日志格式测试工具")
    print("="*60)
    
    # 测试内置样例
    test_log_format()
    
    # 如果提供了文件路径，测试真实文件
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        sample_lines = int(sys.argv[2]) if len(sys.argv) > 2 else 10
        test_real_log_sample(file_path, sample_lines)
    else:
        print("\n💡 使用方法:")
        print("python test_log_format.py [日志文件路径] [测试行数]")
        print("例如: python test_log_format.py 20250728.log 20")


if __name__ == "__main__":
    main()
